// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();

import express from 'express';
import cors from 'cors';
import multer from 'multer';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';
import fs from 'fs';
import { exec } from 'child_process';
import { promisify } from 'util';
import { sendShareEmail, EmailShareRequest } from './lib/email-service';
import { analyticsService, AnalyticsEventType } from './lib/analytics-service';
import type { AnalyticsSummary, PerformanceMetrics } from './lib/analytics-service';

const app = express();
const port = process.env.PORT || 3000;

// Promisify exec for async/await usage
const execAsync = promisify(exec);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const transferId = req.body.transferId || uuidv4();
    cb(null, `${transferId}-${file.originalname}`);
  }
});

// Function to compress file using ZMT
async function compressFileWithZMT(inputFilePath: string, transferId: string): Promise<{ compressedPath: string; compressionRatio: number; compressedSize: number }> {
  const inputDir = path.dirname(inputFilePath);
  const inputFileName = path.basename(inputFilePath);
  const compressedFileName = `${transferId}.zmt`;
  const compressedPath = path.join(inputDir, compressedFileName);

  // Get the absolute path to the ZMT binary
  const zmtBinaryPath = path.join(__dirname, '../../../scripts/zmt');

  try {
    // Get original file size
    const originalStats = fs.statSync(inputFilePath);
    const originalSize = originalStats.size;

    // Run ZMT compression with relative path by changing to the input directory
    // This ensures the file is stored with a relative path for proper extraction
    const command = `cd "${inputDir}" && "${zmtBinaryPath}" a "${compressedFileName}" "${inputFileName}"`;
    console.log(`Running ZMT compression: ${command}`);

    const { stdout, stderr } = await execAsync(command);
    console.log('ZMT compression output:', stdout);
    if (stderr) {
      console.log('ZMT compression stderr:', stderr);
    }

    // Check if compressed file was created
    if (!fs.existsSync(compressedPath)) {
      throw new Error('Compressed file was not created');
    }

    // Get compressed file size
    const compressedStats = fs.statSync(compressedPath);
    const compressedSize = compressedStats.size;

    // Calculate compression ratio (percentage reduction)
    const compressionRatio = (originalSize - compressedSize) / originalSize;

    console.log(`Compression completed: ${originalSize} bytes -> ${compressedSize} bytes (${(compressionRatio * 100).toFixed(1)}% reduction)`);

    return {
      compressedPath,
      compressionRatio,
      compressedSize
    };
  } catch (error) {
    console.error('ZMT compression failed:', error);
    throw error;
  }
}

// Function to decompress ZMT file
async function decompressZMTFile(compressedFilePath: string, outputDir: string): Promise<string[]> {
  // Get the absolute path to the ZMT binary
  const zmtBinaryPath = path.join(__dirname, '../../../scripts/zmt');

  try {
    // Run ZMT extraction with force overwrite: zmt x archive.zmt -force
    const command = `cd "${outputDir}" && "${zmtBinaryPath}" x "${compressedFilePath}" -force`;
    console.log(`Running ZMT decompression: ${command}`);

    const { stdout, stderr } = await execAsync(command);
    console.log('ZMT decompression output:', stdout);
    if (stderr) {
      console.log('ZMT decompression stderr:', stderr);
    }

    // List files in the output directory to see what was extracted
    const extractedFiles: string[] = [];
    const files = fs.readdirSync(outputDir);

    for (const file of files) {
      const filePath = path.join(outputDir, file);
      const stats = fs.statSync(filePath);
      if (stats.isFile()) {
        extractedFiles.push(file);
      }
    }

    console.log(`Decompression completed. Extracted files: ${extractedFiles.join(', ')}`);

    return extractedFiles;
  } catch (error) {
    console.error('ZMT decompression failed:', error);
    throw error;
  }
}

const upload = multer({ storage });

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage for demo purposes (in production, this would be DynamoDB)
interface Transfer {
  transferId: string;
  filename: string;
  originalName: string;
  size: number;
  compressedSize?: number;
  status: 'uploading' | 'compressing' | 'ready' | 'error';
  downloadUrl?: string;
  compressionRatio?: number;
  createdAt: Date;
  filePath?: string;
  compressedPath?: string;
  expiresAt?: number;
  downloadLimit?: number;
  downloadCount?: number;
  password?: string;
}

const transfers: Map<string, Transfer> = new Map();

// Routes

// Upload endpoint
app.post('/api/upload', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const transferId = req.body.transferId || uuidv4();
    const transfer: Transfer = {
      transferId,
      filename: req.file.filename,
      originalName: req.file.originalname,
      size: req.file.size,
      status: 'uploading',
      createdAt: new Date(),
      filePath: req.file.path
    };

    transfers.set(transferId, transfer);

    // Start analytics tracking
    analyticsService.startPerformanceTracking(transferId, req.file.size);
    analyticsService.trackEvent(
      AnalyticsEventType.UPLOAD_COMPLETED,
      {
        fileName: req.file.originalname,
        fileSize: req.file.size,
        userAgent: req.headers['user-agent'],
        ipAddress: req.ip
      },
      transferId
    );

    // Start real ZMT compression
    setTimeout(async () => {
      const currentTransfer = transfers.get(transferId);
      if (currentTransfer) {
        currentTransfer.status = 'compressing';
        transfers.set(transferId, currentTransfer);

        // Track compression start
        analyticsService.updatePerformanceMetrics(transferId, { compressionStartTime: Date.now() });
        analyticsService.trackEvent(AnalyticsEventType.COMPRESSION_STARTED, { fileName: currentTransfer.originalName }, transferId);

        try {
          // Perform real ZMT compression
          const { compressedPath, compressionRatio, compressedSize } = await compressFileWithZMT(currentTransfer.filePath!, transferId);

          const finalTransfer = transfers.get(transferId);
          if (finalTransfer) {
            finalTransfer.status = 'ready';
            finalTransfer.downloadUrl = `/api/download/${transferId}`;
            finalTransfer.compressionRatio = compressionRatio;
            finalTransfer.compressedPath = compressedPath;
            finalTransfer.compressedSize = compressedSize;
            transfers.set(transferId, finalTransfer);

            // Track compression completion
            analyticsService.updatePerformanceMetrics(transferId, {
              compressionEndTime: Date.now(),
              compressedFileSize: compressedSize
            });
            analyticsService.trackEvent(
              AnalyticsEventType.COMPRESSION_COMPLETED,
              {
                fileName: finalTransfer.originalName,
                compressionRatio,
                originalSize: finalTransfer.size,
                compressedSize
              },
              transferId
            );
          }
        } catch (error) {
          console.error('Compression failed:', error);
          const errorTransfer = transfers.get(transferId);
          if (errorTransfer) {
            errorTransfer.status = 'error';
            transfers.set(transferId, errorTransfer);

            // Track compression failure
            analyticsService.trackEvent(
              AnalyticsEventType.COMPRESSION_FAILED,
              {
                fileName: errorTransfer.originalName,
                errorMessage: error instanceof Error ? error.message : 'Unknown error'
              },
              transferId
            );
          }
        }
      }
    }, 1000); // 1 second upload processing

    res.json({
      transferId,
      message: 'File uploaded successfully'
    });
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ error: 'Upload failed' });
  }
});

// Get transfer status
app.get('/api/transfer/:transferId/status', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  res.json({
    transferId: transfer.transferId,
    status: transfer.status,
    downloadUrl: transfer.downloadUrl,
    compressionRatio: transfer.compressionRatio,
    filename: transfer.originalName,
    size: transfer.size
  });
});

// Download endpoint - compressed file (default)
app.get('/api/download/:transferId', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: 'Transfer not ready for download' });
  }

  // Use compressed file if available, otherwise fall back to original
  const fileToDownload = transfer.compressedPath || transfer.filePath;

  if (!fileToDownload || !fs.existsSync(fileToDownload)) {
    return res.status(404).json({ error: 'File not found' });
  }

  // Track download start
  analyticsService.updatePerformanceMetrics(transferId, { downloadStartTime: Date.now() });
  analyticsService.trackEvent(
    AnalyticsEventType.DOWNLOAD_STARTED,
    {
      fileName: transfer.originalName,
      fileSize: transfer.size,
      userAgent: req.headers['user-agent'],
      ipAddress: req.ip
    },
    transferId
  );

  // Set headers for file download
  res.setHeader('Content-Disposition', `attachment; filename="${transfer.originalName}.zmt"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  // Stream the compressed file
  const fileStream = fs.createReadStream(fileToDownload);

  // Track download completion
  fileStream.on('end', () => {
    analyticsService.updatePerformanceMetrics(transferId, { downloadEndTime: Date.now() });
    analyticsService.trackEvent(
      AnalyticsEventType.DOWNLOAD_COMPLETED,
      {
        fileName: transfer.originalName,
        fileSize: transfer.size
      },
      transferId
    );
  });

  // Track download errors
  fileStream.on('error', (error) => {
    analyticsService.trackEvent(
      AnalyticsEventType.DOWNLOAD_FAILED,
      {
        fileName: transfer.originalName,
        errorMessage: error.message
      },
      transferId
    );
  });

  fileStream.pipe(res);
});

// Download original (uncompressed) file endpoint
app.get('/api/download/:transferId/original', async (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: 'Transfer not ready for download' });
  }

  try {
    let fileToDownload: string;
    let downloadFileName: string;

    // If we have a compressed file, we need to decompress it first
    if (transfer.compressedPath && fs.existsSync(transfer.compressedPath)) {
      // Create a temporary directory for decompression
      const tempDecompressDir = path.join(__dirname, '../temp_decompress', transferId);

      if (!fs.existsSync(tempDecompressDir)) {
        fs.mkdirSync(tempDecompressDir, { recursive: true });
      }

      // Decompress the ZMT file
      console.log(`Decompressing ${transfer.compressedPath} for original download`);
      const extractedFiles = await decompressZMTFile(transfer.compressedPath, tempDecompressDir);

      if (extractedFiles.length === 0) {
        return res.status(500).json({ error: 'Failed to decompress file' });
      }

      // Use the first extracted file (should be the original file)
      const extractedFileName = extractedFiles[0];
      fileToDownload = path.join(tempDecompressDir, extractedFileName);
      downloadFileName = transfer.originalName; // Use original name without .zmt extension

      // Clean up temp directory after download
      res.on('finish', () => {
        setTimeout(() => {
          try {
            if (fs.existsSync(tempDecompressDir)) {
              fs.rmSync(tempDecompressDir, { recursive: true, force: true });
            }
          } catch (error) {
            console.error('Failed to clean up temp directory:', error);
          }
        }, 1000); // Wait 1 second before cleanup
      });

    } else if (transfer.filePath && fs.existsSync(transfer.filePath)) {
      // Use original file directly if no compressed version exists
      fileToDownload = transfer.filePath;
      downloadFileName = transfer.originalName;
    } else {
      return res.status(404).json({ error: 'Original file not found' });
    }

    if (!fs.existsSync(fileToDownload)) {
      return res.status(404).json({ error: 'File not found after decompression' });
    }

    // Track download analytics
    analyticsService.trackEvent(
      AnalyticsEventType.DOWNLOAD_STARTED,
      {
        fileName: transfer.originalName,
        downloadType: 'original',
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip
      },
      transferId
    );

    // Set headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${downloadFileName}"`);
    res.setHeader('Content-Type', 'application/octet-stream');

    // Stream the original file
    const fileStream = fs.createReadStream(fileToDownload);

    // Track download completion
    fileStream.on('end', () => {
      analyticsService.updatePerformanceMetrics(transferId, { downloadEndTime: Date.now() });
      analyticsService.trackEvent(
        AnalyticsEventType.DOWNLOAD_COMPLETED,
        {
          fileName: transfer.originalName,
          downloadType: 'original',
          fileSize: transfer.size
        },
        transferId
      );
    });

    fileStream.on('error', (error) => {
      console.error('Download stream error:', error);
      analyticsService.trackEvent(
        AnalyticsEventType.ERROR_OCCURRED,
        {
          errorType: 'download_stream_error',
          errorMessage: error.message,
          fileName: transfer.originalName
        },
        transferId
      );
    });

    fileStream.pipe(res);

  } catch (error) {
    console.error('Original download error:', error);
    analyticsService.trackEvent(
      AnalyticsEventType.ERROR_OCCURRED,
      {
        errorType: 'original_download_failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        fileName: transfer.originalName
      },
      transferId
    );
    res.status(500).json({ error: 'Failed to prepare original file for download' });
  }
});

// Decompress endpoint
app.post('/api/decompress', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No ZMT file uploaded' });
    }

    const decompressId = uuidv4();
    const outputDir = path.join(__dirname, '../decompressed', decompressId);

    // Create output directory
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Decompress the ZMT file
    const extractedFiles = await decompressZMTFile(req.file.path, outputDir);

    // Clean up uploaded ZMT file
    fs.unlinkSync(req.file.path);

    res.json({
      decompressId,
      extractedFiles,
      message: 'File decompressed successfully'
    });
  } catch (error) {
    console.error('Decompression error:', error);
    res.status(500).json({ error: 'Decompression failed' });
  }
});

// Download decompressed file
app.get('/api/decompress/:decompressId/:filename', (req, res) => {
  const { decompressId, filename } = req.params;
  const filePath = path.join(__dirname, '../decompressed', decompressId, filename);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: 'Decompressed file not found' });
  }

  // Set headers for file download
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.setHeader('Content-Type', 'application/octet-stream');

  // Stream the decompressed file
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);
});

// Get all transfers with filtering, sorting, and pagination
app.get('/api/transfers', (req, res) => {
  try {
    const {
      status,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = '1',
      limit = '50',
      startDate,
      endDate
    } = req.query;

    let transferList = Array.from(transfers.values());

    // Filter by status
    if (status && status !== 'all') {
      transferList = transferList.filter(transfer => transfer.status === status);
    }

    // Filter by search query (filename)
    if (search) {
      const searchLower = (search as string).toLowerCase();
      transferList = transferList.filter(transfer =>
        transfer.originalName.toLowerCase().includes(searchLower)
      );
    }

    // Filter by date range
    if (startDate) {
      const start = new Date(startDate as string).getTime();
      transferList = transferList.filter(transfer =>
        transfer.createdAt.getTime() >= start
      );
    }
    if (endDate) {
      const end = new Date(endDate as string).getTime();
      transferList = transferList.filter(transfer =>
        transfer.createdAt.getTime() <= end
      );
    }

    // Sort transfers
    transferList.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'filename':
          aValue = a.originalName.toLowerCase();
          bValue = b.originalName.toLowerCase();
          break;
        case 'size':
          aValue = a.size;
          bValue = b.size;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'createdAt':
        default:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    // Pagination
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedTransfers = transferList.slice(startIndex, endIndex);

    // Format response
    const formattedTransfers = paginatedTransfers.map(transfer => ({
      transferId: transfer.transferId,
      status: transfer.status,
      downloadUrl: transfer.downloadUrl,
      compressionRatio: transfer.compressionRatio,
      filename: transfer.originalName,
      size: transfer.size,
      createdAt: transfer.createdAt,
      expiresAt: transfer.expiresAt,
      downloadCount: transfer.downloadCount || 0,
      downloadLimit: transfer.downloadLimit
    }));

    res.json({
      transfers: formattedTransfers,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: transferList.length,
        totalPages: Math.ceil(transferList.length / limitNum),
        hasNext: endIndex < transferList.length,
        hasPrev: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Error getting transfers:', error);
    res.status(500).json({ error: 'Failed to get transfers' });
  }
});

// Generate share link
app.post('/api/link', (req, res) => {
  const { transferId, expirationHours, downloadLimit, password } = req.body;

  if (!transferId) {
    return res.status(400).json({ error: 'Transfer ID is required' });
  }

  const transfer = transfers.get(transferId);
  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  if (transfer.status !== 'ready') {
    return res.status(400).json({ error: `Transfer is not ready for sharing. Current status: ${transfer.status}` });
  }

  // Update transfer with link settings
  if (expirationHours) {
    transfer.expiresAt = Date.now() + (expirationHours * 60 * 60 * 1000);
  }
  if (downloadLimit !== undefined) {
    transfer.downloadLimit = downloadLimit;
  }
  if (password) {
    transfer.password = password; // In production, this should be hashed
  }

  // Generate share URL - use frontend URL in development, backend URL in production
  const frontendUrl = process.env.FRONTEND_DEV_URL || `${req.protocol}://${req.get('host')}`;
  const shareUrl = `${frontendUrl}/share/${transferId}`;

  res.json({
    transferId,
    shareUrl,
    expiresAt: transfer.expiresAt,
    downloadLimit: transfer.downloadLimit,
  });
});

// Get transfer info for shared link
app.get('/api/transfer/:transferId/info', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  // Check if transfer is expired
  if (transfer.expiresAt && Date.now() > transfer.expiresAt) {
    return res.status(410).json({ error: 'Transfer has expired' });
  }

  // Return public transfer info (without sensitive data)
  const publicInfo = {
    transferId: transfer.transferId,
    status: transfer.status,
    originalName: transfer.originalName,
    size: transfer.size,
    compressedSize: transfer.compressedSize,
    compressionRatio: transfer.compressionRatio,
    createdAt: transfer.createdAt,
    expiresAt: transfer.expiresAt,
    downloadCount: transfer.downloadCount || 0,
    downloadLimit: transfer.downloadLimit,
    hasPassword: !!transfer.password,
  };

  res.json(publicInfo);
});

// Delete transfer
app.delete('/api/transfer/:transferId', (req, res) => {
  const { transferId } = req.params;
  const transfer = transfers.get(transferId);

  if (!transfer) {
    return res.status(404).json({ error: 'Transfer not found' });
  }

  // Delete original file if it exists
  if (transfer.filePath && fs.existsSync(transfer.filePath)) {
    fs.unlinkSync(transfer.filePath);
  }

  // Delete compressed file if it exists
  if (transfer.compressedPath && fs.existsSync(transfer.compressedPath)) {
    fs.unlinkSync(transfer.compressedPath);
  }

  transfers.delete(transferId);
  res.json({ message: 'Transfer deleted successfully' });
});

// Bulk delete transfers
app.post('/api/transfers/bulk-delete', (req, res) => {
  try {
    const { transferIds } = req.body;

    if (!Array.isArray(transferIds) || transferIds.length === 0) {
      return res.status(400).json({ error: 'Transfer IDs array is required' });
    }

    const deletedTransfers = [];
    const failedDeletes = [];

    for (const transferId of transferIds) {
      const transfer = transfers.get(transferId);

      if (!transfer) {
        failedDeletes.push({ transferId, error: 'Transfer not found' });
        continue;
      }

      try {
        // Delete original file if it exists
        if (transfer.filePath && fs.existsSync(transfer.filePath)) {
          fs.unlinkSync(transfer.filePath);
        }

        // Delete compressed file if it exists
        if (transfer.compressedPath && fs.existsSync(transfer.compressedPath)) {
          fs.unlinkSync(transfer.compressedPath);
        }

        transfers.delete(transferId);
        deletedTransfers.push(transferId);
      } catch (error) {
        failedDeletes.push({ transferId, error: 'Failed to delete files' });
      }
    }

    res.json({
      message: `Successfully deleted ${deletedTransfers.length} transfers`,
      deleted: deletedTransfers,
      failed: failedDeletes
    });
  } catch (error) {
    console.error('Bulk delete error:', error);
    res.status(500).json({ error: 'Failed to delete transfers' });
  }
});

// Update transfer metadata
app.patch('/api/transfer/:transferId', (req, res) => {
  try {
    const { transferId } = req.params;
    const { downloadLimit, expirationHours, password } = req.body;

    const transfer = transfers.get(transferId);
    if (!transfer) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    // Update download limit
    if (downloadLimit !== undefined) {
      transfer.downloadLimit = downloadLimit;
    }

    // Update expiration
    if (expirationHours !== undefined) {
      if (expirationHours === 0) {
        transfer.expiresAt = undefined; // Remove expiration
      } else {
        transfer.expiresAt = Date.now() + (expirationHours * 60 * 60 * 1000);
      }
    }

    // Update password
    if (password !== undefined) {
      transfer.password = password || undefined; // In production, this should be hashed
    }

    transfers.set(transferId, transfer);

    res.json({
      message: 'Transfer updated successfully',
      transfer: {
        transferId: transfer.transferId,
        downloadLimit: transfer.downloadLimit,
        expiresAt: transfer.expiresAt,
        hasPassword: !!transfer.password
      }
    });
  } catch (error) {
    console.error('Update transfer error:', error);
    res.status(500).json({ error: 'Failed to update transfer' });
  }
});

// Send email with download link
app.post('/api/email', async (req, res) => {
  try {
    const { transferId, recipientEmail, senderName } = req.body;

    // Validate required fields
    if (!transferId) {
      return res.status(400).json({ error: 'Transfer ID is required' });
    }

    if (!recipientEmail) {
      return res.status(400).json({ error: 'Recipient email is required' });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(recipientEmail)) {
      return res.status(400).json({ error: 'Invalid email address format' });
    }

    // Get transfer
    const transfer = transfers.get(transferId);
    if (!transfer) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    // Check if transfer is ready for sharing
    if (transfer.status !== 'ready') {
      return res.status(400).json({ error: `Transfer is not ready for sharing. Current status: ${transfer.status}` });
    }

    // Check if transfer is expired
    if (transfer.expiresAt && Date.now() > transfer.expiresAt) {
      return res.status(410).json({ error: 'Transfer has expired' });
    }

    // Check if download limit has been reached
    if (transfer.downloadLimit && (transfer.downloadCount || 0) >= transfer.downloadLimit) {
      return res.status(410).json({ error: 'Download limit has been reached' });
    }

    // Generate download URL - use frontend URL in development, backend URL in production
    const frontendUrl = process.env.FRONTEND_DEV_URL || `${req.protocol}://${req.get('host')}`;
    const downloadUrl = `${frontendUrl}/share/${transferId}`;

    // Prepare email data with file details
    const files = [{
      name: transfer.originalName,
      size: transfer.size,
      originalSize: transfer.size // For single file, original size is the same
    }];

    const emailData: EmailShareRequest = {
      transferId,
      recipientEmail,
      senderName,
      shareLink: downloadUrl,
      files,
      totalSize: transfer.size,
      totalOriginalSize: transfer.size,
      compressionRatio: transfer.compressionRatio,
      expiresAt: transfer.expiresAt ? new Date(transfer.expiresAt).toISOString() : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      downloadLimit: transfer.downloadLimit || 10,
    };

    // Send email
    const emailResult = await sendShareEmail(emailData);

    if (!emailResult.success) {
      // Track email failure
      analyticsService.trackEvent(
        AnalyticsEventType.ERROR_OCCURRED,
        {
          errorType: 'email_send_failed',
          errorMessage: emailResult.error,
          recipientEmail,
          fileName: transfer.originalName
        },
        transferId
      );
      return res.status(500).json({ error: `Failed to send email: ${emailResult.error}` });
    }

    // Track successful email send
    analyticsService.trackEvent(
      AnalyticsEventType.SHARE_EMAIL_SENT,
      {
        recipientEmail,
        fileName: transfer.originalName,
        fileSize: transfer.size,
        compressionRatio: transfer.compressionRatio
      },
      transferId
    );

    res.json({
      success: true,
    });

  } catch (error) {
    console.error('Email endpoint error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Analytics endpoints
app.get('/api/analytics/summary', (req, res) => {
  try {
    const timeRange = req.query.timeRange ? {
      start: parseInt(req.query.start as string),
      end: parseInt(req.query.end as string)
    } : undefined;

    const summary = analyticsService.getAnalyticsSummary(timeRange);
    res.json(summary);
  } catch (error) {
    console.error('Analytics summary error:', error);
    res.status(500).json({ error: 'Failed to get analytics summary' });
  }
});

app.get('/api/analytics/events', (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;
    const events = analyticsService.getRecentEvents(limit);
    res.json(events);
  } catch (error) {
    console.error('Analytics events error:', error);
    res.status(500).json({ error: 'Failed to get analytics events' });
  }
});

app.get('/api/analytics/performance/:transferId', (req, res) => {
  try {
    const { transferId } = req.params;
    const metrics = analyticsService.getPerformanceMetrics(transferId);

    if (!metrics) {
      return res.status(404).json({ error: 'Performance metrics not found' });
    }

    res.json(metrics);
  } catch (error) {
    console.error('Performance metrics error:', error);
    res.status(500).json({ error: 'Failed to get performance metrics' });
  }
});

// Get transfer statistics
app.get('/api/transfers/stats', (req, res) => {
  try {
    const transferList = Array.from(transfers.values());

    const stats = {
      total: transferList.length,
      byStatus: {
        uploading: transferList.filter(t => t.status === 'uploading').length,
        compressing: transferList.filter(t => t.status === 'compressing').length,
        ready: transferList.filter(t => t.status === 'ready').length,
        error: transferList.filter(t => t.status === 'error').length
      },
      totalSize: transferList.reduce((acc, t) => acc + t.size, 0),
      totalCompressedSize: transferList.reduce((acc, t) => acc + (t.compressedPath ? (t.size * (t.compressionRatio || 1)) : 0), 0),
      averageCompressionRatio: transferList.length > 0
        ? transferList.reduce((acc, t) => acc + (t.compressionRatio || 0), 0) / transferList.length
        : 0,
      totalDownloads: transferList.reduce((acc, t) => acc + (t.downloadCount || 0), 0),
      recentActivity: transferList
        .filter(t => t.createdAt.getTime() > Date.now() - (7 * 24 * 60 * 60 * 1000)) // Last 7 days
        .length,
      storageUsed: transferList.reduce((acc, t) => {
        let size = t.size; // Original file size
        if (t.compressedPath && t.compressionRatio) {
          size += t.size * t.compressionRatio; // Add compressed file size
        }
        return acc + size;
      }, 0)
    };

    res.json(stats);
  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({ error: 'Failed to get transfer statistics' });
  }
});

// Health check
app.get('/api/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// File download endpoint with custom cache headers based on file type
app.get('/api/download/:transferId/:filename', (req, res) => {
  try {
    const { transferId, filename } = req.params;

    // Validate transfer exists
    const transfer = transfers.get(transferId);
    if (!transfer) {
      return res.status(404).json({ error: 'Transfer not found' });
    }

    if (transfer.status !== 'ready') {
      return res.status(400).json({ error: 'Transfer not ready for download' });
    }

    // Get file extension for cache policy
    const fileExt = path.extname(filename).toLowerCase();

    // Set custom cache headers based on file type
    if (fileExt === '.zmt') {
      // ZMT files - medium-term cache, no compression
      res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
      res.setHeader('Content-Encoding', 'identity');
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Type', 'application/octet-stream');
    } else if (['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2'].includes(fileExt)) {
      // Archive files - medium-term cache, no compression
      res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
      res.setHeader('Content-Encoding', 'identity');
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Content-Type', 'application/octet-stream');
    } else if (['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'].includes(fileExt)) {
      // Document files - daily cache with range support
      res.setHeader('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600'); // 1 day
      res.setHeader('Accept-Ranges', 'bytes');
      res.setHeader('Vary', 'Accept-Encoding');
    } else if (['.mp4', '.avi', '.mov', '.wmv', '.mp3', '.wav', '.flac', '.aac'].includes(fileExt)) {
      // Media files - long-term cache with range support for streaming
      res.setHeader('Cache-Control', 'public, max-age=2592000, stale-while-revalidate=86400'); // 30 days
      res.setHeader('Content-Encoding', 'identity');
      res.setHeader('Accept-Ranges', 'bytes');
    } else if (['.html', '.htm'].includes(fileExt)) {
      // HTML files - short cache with revalidation
      res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate, stale-while-revalidate=60'); // 5 minutes
      res.setHeader('Vary', 'Accept-Encoding');
    } else if (['.txt', '.log', '.csv', '.json', '.xml'].includes(fileExt)) {
      // Text files - hourly cache with compression
      res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
      res.setHeader('Vary', 'Accept-Encoding');
    } else {
      // Default cache for unknown file types
      res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
      res.setHeader('Vary', 'Accept-Encoding');
    }

    // Set security and download headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // Track download analytics
    analyticsService.trackEvent(AnalyticsEventType.DOWNLOAD_STARTED, {
      filename,
      fileExtension: fileExt,
      userAgent: req.get('User-Agent'),
      fileSize: transfer.size
    }, transferId);

    // Use compressed file if available, otherwise fall back to original
    const fileToDownload = transfer.compressedPath || transfer.filePath;

    if (!fileToDownload || !fs.existsSync(fileToDownload)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Stream the file with appropriate headers
    res.download(fileToDownload, filename, (err) => {
      if (err) {
        console.error('Download error:', err);
        analyticsService.trackEvent(AnalyticsEventType.DOWNLOAD_FAILED, {
          filename,
          error: err.message
        }, transferId);
      } else {
        // Track successful download
        analyticsService.trackEvent(AnalyticsEventType.DOWNLOAD_COMPLETED, {
          filename,
          fileExtension: fileExt,
          fileSize: transfer.size
        }, transferId);

        // Update download count if limit is set
        if (transfer.downloadLimit !== undefined) {
          transfer.downloadCount = (transfer.downloadCount || 0) + 1;
          transfers.set(transferId, transfer);
        }
      }
    });

  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Failed to process download request' });
  }
});

// Serve static files from frontend build (if available)
const frontendBuildPath = path.join(__dirname, '../../../packages/frontend/dist');
if (fs.existsSync(frontendBuildPath)) {
  // Configure caching for different asset types
  app.use('/assets', (req, res, next) => {
    // Versioned assets (JS, CSS, images with hashes) - long cache
    if (req.path.match(/\.(js|css|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/)) {
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
      res.setHeader('Expires', new Date(Date.now() + 31536000000).toUTCString());
    }
    next();
  }, express.static(path.join(frontendBuildPath, 'assets')));

  // Serve other static files with custom cache policies
  app.use(express.static(frontendBuildPath, {
    setHeaders: (res, filePath) => {
      // Set common headers for all files
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');

      if (filePath.endsWith('.html')) {
        // HTML files - short cache for updates with revalidation
        res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate, stale-while-revalidate=60');
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(js|css)$/)) {
        // Non-versioned JS/CSS - medium cache
        res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300');
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(png|jpg|jpeg|gif|svg|ico|webp|avif)$/)) {
        // Images - medium cache with immutable for hashed files
        const isVersioned = filePath.includes('-') && filePath.match(/[a-f0-9]{8,}/);
        if (isVersioned) {
          res.setHeader('Cache-Control', 'public, max-age=31536000, immutable'); // 1 year
        } else {
          res.setHeader('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600'); // 1 day
        }
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(woff|woff2|ttf|eot)$/)) {
        // Fonts - long cache with CORS headers
        res.setHeader('Cache-Control', 'public, max-age=2592000, immutable'); // 30 days
        res.setHeader('Access-Control-Allow-Origin', '*');
        res.setHeader('Access-Control-Allow-Methods', 'GET');
      } else if (filePath.match(/\.(zmt)$/)) {
        // ZMT compressed files - medium-term cache with range support
        res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Encoding', 'identity'); // Don't double-compress
      } else if (filePath.match(/\.(zip|rar|7z|tar|gz|bz2)$/)) {
        // Archive files - medium cache with range support
        res.setHeader('Cache-Control', 'public, max-age=604800, stale-while-revalidate=86400'); // 7 days
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Encoding', 'identity'); // Don't compress archives
      } else if (filePath.match(/\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/)) {
        // Document files - medium cache with range support
        res.setHeader('Cache-Control', 'public, max-age=86400, stale-while-revalidate=3600'); // 1 day
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Vary', 'Accept-Encoding');
      } else if (filePath.match(/\.(mp4|avi|mov|wmv|flv|webm|mkv|mp3|wav|flac|aac)$/)) {
        // Media files - long cache with range support for streaming
        res.setHeader('Cache-Control', 'public, max-age=2592000, stale-while-revalidate=86400'); // 30 days
        res.setHeader('Accept-Ranges', 'bytes');
        res.setHeader('Content-Encoding', 'identity'); // Don't compress media
      } else if (filePath.match(/\.(txt|log|csv|json|xml)$/)) {
        // Text files - short cache with compression
        res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
        res.setHeader('Vary', 'Accept-Encoding');
      } else {
        // Default cache for unknown file types
        res.setHeader('Cache-Control', 'public, max-age=3600, stale-while-revalidate=300'); // 1 hour
        res.setHeader('Vary', 'Accept-Encoding');
      }
    }
  }));

  // Handle client-side routing - serve index.html for non-API routes
  app.get('*', (req, res) => {
    // Don't serve index.html for API routes
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    // Set cache headers for HTML
    res.setHeader('Cache-Control', 'public, max-age=300, must-revalidate');
    res.sendFile(path.join(frontendBuildPath, 'index.html'));
  });
} else {
  // Development fallback - redirect share links to frontend dev server
  app.get('/share/:transferId', (req, res) => {
    const { transferId } = req.params;
    // In development, redirect to frontend dev server
    const frontendDevUrl = process.env.FRONTEND_DEV_URL || 'http://localhost:5174';
    res.redirect(`${frontendDevUrl}/share/${transferId}`);
  });

  // For other non-API routes in development
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    const frontendDevUrl = process.env.FRONTEND_DEV_URL || 'http://localhost:5174';
    res.redirect(`${frontendDevUrl}${req.path}`);
  });
}

// Start server
app.listen(port, () => {
  console.log(`FastTransfer backend server running on port ${port}`);
  console.log(`API endpoints available at http://localhost:${port}/api`);

  const frontendBuildExists = fs.existsSync(frontendBuildPath);
  if (frontendBuildExists) {
    console.log(`Serving frontend from: ${frontendBuildPath}`);
  } else {
    console.log(`Frontend build not found. Development mode - redirecting to frontend dev server`);
    console.log(`Make sure frontend dev server is running on ${process.env.FRONTEND_DEV_URL || 'http://localhost:5173'}`);
  }
});

export default app;
